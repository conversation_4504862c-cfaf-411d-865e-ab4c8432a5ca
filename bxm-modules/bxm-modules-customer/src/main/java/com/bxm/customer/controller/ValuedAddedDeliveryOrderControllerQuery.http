### ===================================================================
### ValuedAddedDeliveryOrderController HTTP 测试文件
### 功能：测试增值交付单控制器相关接口
### 作者：system
### 创建时间：2025-01-27
### ===================================================================

### 1. 获取账号子类型字典数据
### 接口：GET /valuedAddedDeliveryOrder/getAccountSubType
### 功能：获取NON_STANDARD字典类型的账号子类型选项列表
### 返回：包含高新账、凭票入账、特殊行业、民非等类型的JSON数组
GET {{baseUrl}}/valuedAddedDeliveryOrder/getAccountSubType
Accept: application/json
Content-Type: application/json

### 2. 获取账号子类型字典数据（带认证头）
### 使用场景：需要认证的环境下测试
GET {{baseUrl}}/valuedAddedDeliveryOrder/getAccountSubType
Accept: application/json
Content-Type: application/json
Authorization: Bearer {{token}}

### 3. 获取账号子类型字典数据（本地测试）
### 本地开发环境测试
GET http://localhost:8080/valuedAddedDeliveryOrder/getAccountSubType
Accept: application/json
Content-Type: application/json

### 4. 获取账号子类型字典数据（开发环境测试）
### 开发环境测试
GET http://dev-server:8080/valuedAddedDeliveryOrder/getAccountSubType
Accept: application/json
Content-Type: application/json

### ===================================================================
### 测试变量配置
### 请根据实际环境配置以下变量
### ===================================================================

# 基础URL配置
# @name baseUrl
# @value http://localhost:8080

# 认证Token配置（如果需要）
# @name token
# @value your_jwt_token_here

### ===================================================================
### 预期响应格式示例
### ===================================================================

###
### 成功响应示例：
### HTTP/1.1 200 OK
### Content-Type: application/json
### 
### {
###   "code": 200,
###   "msg": "操作成功",
###   "data": [
###     {
###       "value": "HIGH_TECH",
###       "label": "高新账",
###       "sort": 1,
###       "cssClass": "success",
###       "listClass": "",
###       "isDefault": "N"
###     },
###     {
###       "value": "VOUCHER_BASED",
###       "label": "凭票入账",
###       "sort": 2,
###       "cssClass": "info",
###       "listClass": "",
###       "isDefault": "N"
###     },
###     {
###       "value": "SPECIAL_INDUSTRY",
###       "label": "特殊行业",
###       "sort": 3,
###       "cssClass": "warning",
###       "listClass": "",
###       "isDefault": "N"
###     },
###     {
###       "value": "NON_PROFIT",
###       "label": "民非",
###       "sort": 4,
###       "cssClass": "primary",
###       "listClass": "",
###       "isDefault": "N"
###     }
###   ]
### }

###
### 错误响应示例：
### HTTP/1.1 500 Internal Server Error
### Content-Type: application/json
### 
### {
###   "code": 500,
###   "msg": "获取账号子类型失败: 字典数据未找到",
###   "data": null
### }

### ===================================================================
### 其他相关接口测试（供参考）
### ===================================================================

### 5. 生成交付单编号
GET {{baseUrl}}/valuedAddedDeliveryOrder/genDeliveryOrderNo
Accept: application/json
Content-Type: application/json

### 6. 获取增值事项类型列表
GET {{baseUrl}}/valuedAddedDeliveryOrder/listItemType
Accept: application/json
Content-Type: application/json

### 7. 条件查询增值交付单（示例）
GET {{baseUrl}}/valuedAddedDeliveryOrder/query?pageNum=1&pageSize=10
Accept: application/json
Content-Type: application/json

### 8. 根据交付单编号查询详细信息（示例）
### 注意：需要替换 {deliveryOrderNo} 为实际的交付单编号
GET {{baseUrl}}/valuedAddedDeliveryOrder/getDeliveryOrder/*******************
Accept: application/json
Content-Type: application/json

### 9. 获取可用状态列表（示例）
### 注意：需要替换 {deliveryOrderNo} 为实际的交付单编号
GET {{baseUrl}}/valuedAddedDeliveryOrder/availableStatuses/*******************
Accept: application/json
Content-Type: application/json

### ===================================================================
### 测试步骤说明
### ===================================================================

###
### 1. 环境准备：
###    - 确保 bxm-modules-customer 模块已启动
###    - 确保数据库中已执行 account_sub_type_dict.sql 脚本
###    - 确保 Redis 缓存服务正常运行
###    - 确保 Nacos 配置中心正常运行
###
### 2. 测试执行：
###    - 首选使用测试用例3（本地测试）进行基础功能验证
###    - 如果需要认证，使用测试用例2，并配置正确的token
###    - 根据部署环境选择合适的baseUrl
###
### 3. 结果验证：
###    - 检查HTTP状态码是否为200
###    - 检查返回的JSON格式是否正确
###    - 验证data字段包含4个账号子类型选项
###    - 验证每个选项包含value、label、sort等必要字段
###
### 4. 异常测试：
###    - 在字典数据不存在时测试接口行为
###    - 在服务异常时测试错误处理机制
###    - 验证日志记录是否完整

### ===================================================================
### 性能测试（可选）
### ===================================================================

### 10. 并发测试 - 连续请求
GET {{baseUrl}}/valuedAddedDeliveryOrder/getAccountSubType

###
GET {{baseUrl}}/valuedAddedDeliveryOrder/getAccountSubType

###
GET {{baseUrl}}/valuedAddedDeliveryOrder/getAccountSubType

###
GET {{baseUrl}}/valuedAddedDeliveryOrder/getAccountSubType

###
GET {{baseUrl}}/valuedAddedDeliveryOrder/getAccountSubType